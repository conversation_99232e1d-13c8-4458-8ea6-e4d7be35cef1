'use client';

import { useState, useEffect, useRef } from 'react';
import { doc, setDoc, collection, getDoc, updateDoc } from 'firebase/firestore';
import { ref, uploadBytes } from 'firebase/storage';
import { db, storage } from '@/lib/firebase';
import { useAuth } from '@/contexts/AuthContext';
import Upperbar from './Upperbar';
import ChatInterface from './ChatInterface';
import InputBar from './InputBar';
import AttachmentDisplay from './AttachmentDisplay';
import DownloadModal from './DownloadModal';
import ModelSelectionModal from './ModelSelectionModal';
import { ChatMessage } from '@/lib/types/chat';
import aiService from '@/lib/services/aiService';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: string;
  isFavorite?: boolean;
  attachments?: import('@/lib/types/chat').AttachmentMetadata[];
}

interface ChatAreaProps {
  currentChat: string | null;
  onChatCreated?: (chatId: string) => void;
}

export default function ChatArea({ currentChat, onChatCreated }: ChatAreaProps) {
  const { user } = useAuth();
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [selectedModel, setSelectedModel] = useState('meta-llama/llama-3.1-8b-instruct:free');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [actualChatId, setActualChatId] = useState<string | null>(currentChat);
  const [isDownloadModalOpen, setIsDownloadModalOpen] = useState(false);
  const [isModelModalOpen, setIsModelModalOpen] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(null);
  const [chatName, setChatName] = useState<string>('Nova Conversa');
  const [isLoadingChat, setIsLoadingChat] = useState(false);
  const [currentUsername, setCurrentUsername] = useState<string | undefined>(undefined);
  const chatInterfaceRef = useRef<HTMLDivElement>(null);

  // Carregar username quando o usuário estiver disponível
  useEffect(() => {
    const loadUsername = async () => {
      if (user?.email) {
        const username = await getUsernameFromFirestore();
        setCurrentUsername(username);
      }
    };
    loadUsername();
  }, [user?.email]);

  // Função utilitária para buscar username correto
  const getUsernameFromFirestore = async (): Promise<string> => {
    if (!user?.email) return 'unknown';

    try {
      const { collection, query, where, getDocs } = await import('firebase/firestore');
      const usuariosRef = collection(db, 'usuarios');
      const q = query(usuariosRef, where('email', '==', user.email));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const userDoc = querySnapshot.docs[0];
        const userData = userDoc.data();
        return userData.username || user.email.split('@')[0];
      }

      return user.email.split('@')[0]; // fallback
    } catch (error) {
      console.error('Erro ao buscar username:', error);
      return user.email.split('@')[0]; // fallback
    }
  };

  // Função para salvar o último modelo usado
  const saveLastUsedModel = async (modelId: string) => {
    if (!user) return;

    try {
      const username = await getUsernameFromFirestore();
      const userRef = doc(db, 'usuarios', username, 'configuracoes', 'settings');

      // Verificar se o documento existe
      const userDoc = await getDoc(userRef);

      if (userDoc.exists()) {
        // Atualizar documento existente
        await updateDoc(userRef, {
          lastUsedModel: modelId,
          lastModelUpdateAt: Date.now()
        });
      } else {
        // Criar novo documento
        await setDoc(userRef, {
          lastUsedModel: modelId,
          lastModelUpdateAt: Date.now()
        }, { merge: true });
      }

      console.log('Last used model saved:', modelId);
    } catch (error) {
      console.error('Error saving last used model:', error);
    }
  };

  // Função para carregar o último modelo usado
  const loadLastUsedModel = async () => {
    if (!user) return;

    try {
      const username = await getUsernameFromFirestore();
      const userRef = doc(db, 'usuarios', username, 'configuracoes', 'settings');
      const userDoc = await getDoc(userRef);

      if (userDoc.exists()) {
        const data = userDoc.data();
        if (data.lastUsedModel) {
          setSelectedModel(data.lastUsedModel);
          console.log('Loaded last used model:', data.lastUsedModel);
        }
      }
    } catch (error) {
      console.error('Error loading last used model:', error);
    }
  };

  // Função wrapper para setSelectedModel que também salva no Firestore
  const handleModelChange = (modelId: string) => {
    setSelectedModel(modelId);
    saveLastUsedModel(modelId);
  };

  // Função para criar um chat automaticamente
  const createAutoChat = async (firstMessage: string): Promise<string | null> => {
    if (!user?.email) return null;

    try {
      // Buscar username do usuário
      const { collection, query, where, getDocs } = await import('firebase/firestore');
      const usuariosRef = collection(db, 'usuarios');
      const q = query(usuariosRef, where('email', '==', user.email));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) return null;

      const userDoc = querySnapshot.docs[0];
      const userData = userDoc.data();
      const username = userData.username;

      // Gerar ID único para o chat
      const timestamp = Date.now();
      const random = Math.random().toString(36).substring(2, 8);
      const chatId = `chat_${timestamp}_${random}`;
      const now = new Date().toISOString();

      // Gerar nome do chat baseado na primeira mensagem (primeiras 3-4 palavras)
      let finalChatName = 'Nova Conversa';
      if (firstMessage.trim().length > 0) {
        const words = firstMessage.trim().split(' ');
        const chatName = words.slice(0, Math.min(4, words.length)).join(' ');
        finalChatName = chatName.length > 30 ? chatName.substring(0, 30) + '...' : chatName;
      }

      // Dados para o Firestore
      const firestoreData = {
        context: '',
        createdAt: now,
        folderId: null,
        frequencyPenalty: 1.0,
        isFixed: false,
        lastUpdatedAt: now,
        lastUsedModel: selectedModel,
        latexInstructions: false,
        maxTokens: 2048,
        name: finalChatName,
        password: '',
        repetitionPenalty: 1.0,
        sessionTime: {
          lastSessionStart: now,
          lastUpdated: now,
          totalTime: 0
        },
        systemPrompt: '',
        temperature: 1.0,
        ultimaMensagem: firstMessage || 'Anexo enviado',
        ultimaMensagemEm: now,
        updatedAt: now
      };

      // Criar documento no Firestore
      await setDoc(doc(db, 'usuarios', username, 'conversas', chatId), firestoreData);

      // Criar arquivo chat.json no Storage
      const chatJsonData = {
        id: chatId,
        name: finalChatName,
        messages: [],
        createdAt: now,
        lastUpdated: now
      };

      const chatJsonBlob = new Blob([JSON.stringify(chatJsonData, null, 2)], {
        type: 'application/json'
      });

      const storageRef = ref(storage, `usuarios/${username}/conversas/${chatId}/chat.json`);
      await uploadBytes(storageRef, chatJsonBlob);

      console.log('Chat criado automaticamente:', chatId);
      return chatId;

    } catch (error) {
      console.error('Erro ao criar chat automaticamente:', error);
      return null;
    }
  };

  const handleSendMessage = async (attachments?: import('@/lib/types/chat').AttachmentMetadata[]) => {
    console.log('=== DEBUG: CHATAREA HANDLE SEND MESSAGE ===');
    console.log('Mensagem:', message);
    console.log('Anexos recebidos:', attachments?.length || 0);
    console.log('Anexos detalhes:', JSON.stringify(attachments, null, 2));

    // Debug: verificar mensagens atuais no estado
    console.log('=== DEBUG: MENSAGENS ATUAIS NO ESTADO ===');
    console.log('Total de mensagens no estado:', messages.length);
    const messagesWithAttachments = messages.filter(msg => msg.attachments && msg.attachments.length > 0);
    console.log('Mensagens com anexos no estado:', messagesWithAttachments.length);
    messagesWithAttachments.forEach((msg, index) => {
      console.log(`Mensagem ${index + 1} com anexos:`, {
        id: msg.id,
        sender: msg.sender,
        attachmentsCount: msg.attachments?.length || 0,
        attachments: msg.attachments
      });
    });

    if ((!message.trim() && (!attachments || attachments.length === 0)) || isLoading || isStreaming) {
      console.log('=== DEBUG: CONDIÇÃO DE RETORNO ===');
      console.log('Mensagem vazia:', !message.trim());
      console.log('Sem anexos:', !attachments || attachments.length === 0);
      console.log('Loading:', isLoading);
      console.log('Streaming:', isStreaming);
      return;
    }
    if (!user?.email) return;

    const userMessage: Message = {
      id: aiService.generateMessageId(),
      content: message.trim(),
      sender: 'user',
      timestamp: new Date().toISOString(),
      attachments: attachments || [],
    };

    // Se não há chat atual, criar um automaticamente
    let chatIdToUse = actualChatId;
    if (!chatIdToUse) {
      const messageForChat = message.trim() || (attachments && attachments.length > 0 ? 'Anexo enviado' : 'Nova conversa');
      chatIdToUse = await createAutoChat(messageForChat);
      if (chatIdToUse) {
        setActualChatId(chatIdToUse);
        // Carregar o nome do chat recém-criado com um pequeno delay
        setTimeout(() => {
          if (chatIdToUse) {
            loadChatName(chatIdToUse);
          }
        }, 100);
        onChatCreated?.(chatIdToUse);
      }
    }

    if (!chatIdToUse) {
      console.error('Não foi possível criar ou obter chat ID');
      return;
    }

    // Adicionar mensagem do usuário
    setMessages(prev => [...prev, userMessage]);
    const currentMessage = message.trim() || ""; // Permitir mensagem vazia se houver anexos
    setMessage('');
    setIsLoading(true);
    setIsStreaming(true);

    // Preparar ID para a mensagem da IA que será criada durante o streaming
    const aiMessageId = aiService.generateMessageId();
    setStreamingMessageId(aiMessageId);

    // Buscar username correto do usuário
    const username = await getUsernameFromFirestore();

    // Enviar para a IA
    await aiService.sendMessageSafe(
      {
        username: username,
        chatId: chatIdToUse,
        message: currentMessage,
        model: selectedModel,
        attachments: attachments || [],
      },
      // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk
      (chunk: string) => {
        setMessages(prev => {
          // Verificar se a mensagem da IA já existe
          const existingMessageIndex = prev.findIndex(msg => msg.id === aiMessageId);

          if (existingMessageIndex !== -1) {
            // Atualizar mensagem existente
            return prev.map(msg =>
              msg.id === aiMessageId
                ? { ...msg, content: msg.content + chunk }
                : msg
            );
          } else {
            // Criar nova mensagem da IA na primeira chunk
            // Remover o indicador de loading assim que a primeira chunk chegar
            setIsLoading(false);

            const aiMessage: Message = {
              id: aiMessageId,
              content: chunk,
              sender: 'ai',
              timestamp: new Date().toISOString(),
            };
            return [...prev, aiMessage];
          }
        });
      },
      // onComplete - finalizar streaming
      (fullResponse: string) => {
        setMessages(prev => prev.map(msg =>
          msg.id === aiMessageId
            ? { ...msg, content: fullResponse }
            : msg
        ));
        setIsLoading(false);
        setIsStreaming(false);
        setStreamingMessageId(null);
      },
      // onError - tratar erros
      (error: string) => {
        console.error('Erro na IA:', error);
        setMessages(prev => prev.map(msg =>
          msg.id === aiMessageId
            ? { ...msg, content: `❌ Erro: ${error}` }
            : msg
        ));
        setIsLoading(false);
        setIsStreaming(false);
        setStreamingMessageId(null);
      }
    );
  };

  // Função para cancelar streaming
  const handleCancelStreaming = () => {
    aiService.cancelRequest();
    setIsLoading(false);
    setIsStreaming(false);
    setStreamingMessageId(null);
  };

  // Função para carregar o nome do chat do Firestore
  const loadChatName = async (chatId: string) => {
    if (!user?.email) return;

    try {
      const username = await getUsernameFromFirestore();
      const chatDoc = await getDoc(doc(db, 'usuarios', username, 'conversas', chatId));

      if (chatDoc.exists()) {
        const chatData = chatDoc.data();
        setChatName(chatData.name || 'Conversa sem nome');
      } else {
        setChatName('Conversa não encontrada');
      }
    } catch (error) {
      console.error('Erro ao carregar nome do chat:', error);
      setChatName('Erro ao carregar nome');
    }
  };

  // Função para carregar mensagens existentes do chat
  const loadChatMessages = async (chatId: string) => {
    if (!user?.email) return;

    setIsLoadingChat(true);
    try {
      const username = await getUsernameFromFirestore();
      const chatMessages = await aiService.loadChatMessages(username, chatId);

      // Debug: verificar mensagens carregadas
      console.log('=== DEBUG: MENSAGENS CARREGADAS DO STORAGE ===');
      console.log('Total de mensagens:', chatMessages.length);
      chatMessages.forEach((msg, index) => {
        console.log(`Mensagem ${index + 1}:`, {
          id: msg.id,
          role: msg.role,
          hasAttachments: !!(msg.attachments && msg.attachments.length > 0),
          attachmentsCount: msg.attachments?.length || 0,
          attachments: msg.attachments
        });
      });

      const convertedMessages = aiService.convertFromAIFormat(chatMessages);

      // Debug: verificar mensagens convertidas
      console.log('=== DEBUG: MENSAGENS CONVERTIDAS ===');
      console.log('Total de mensagens convertidas:', convertedMessages.length);
      convertedMessages.forEach((msg, index) => {
        console.log(`Mensagem convertida ${index + 1}:`, {
          id: msg.id,
          sender: msg.sender,
          hasAttachments: !!(msg.attachments && msg.attachments.length > 0),
          attachmentsCount: msg.attachments?.length || 0,
          attachments: msg.attachments
        });
      });

      setMessages(convertedMessages);
    } catch (error) {
      console.error('Erro ao carregar mensagens do chat:', error);
      setMessages([]);
    } finally {
      setIsLoadingChat(false);
    }
  };

  // Carregar último modelo usado quando o componente montar
  useEffect(() => {
    if (user) {
      loadLastUsedModel();
    }
  }, [user]);

  // Carregar mensagens quando o chat atual mudar
  useEffect(() => {
    if (currentChat && currentChat !== actualChatId) {
      setActualChatId(currentChat);
      setIsLoadingChat(true);
      // Limpar mensagens imediatamente para mostrar o estado de carregamento
      setMessages([]);
      loadChatMessages(currentChat);
      loadChatName(currentChat);
    } else if (!currentChat) {
      setActualChatId(null);
      setMessages([]);
      setChatName('Nova Conversa');
      setIsLoadingChat(false);
    }
  }, [currentChat, user?.email]);

  // Funções para manipular mensagens
  const handleDeleteMessage = async (messageId: string) => {
    if (!actualChatId || !user?.email) return;

    // Remover visualmente primeiro para melhor UX
    setMessages(prev => prev.filter(msg => msg.id !== messageId));

    try {
      const username = await getUsernameFromFirestore();
      const success = await aiService.deleteMessage(username, actualChatId, messageId);

      if (!success) {
        // Se falhou, restaurar a mensagem
        loadChatMessages(actualChatId);
        console.error('Falha ao deletar mensagem no servidor');
      }
    } catch (error) {
      // Se falhou, restaurar a mensagem
      loadChatMessages(actualChatId);
      console.error('Erro ao deletar mensagem:', error);
    }
  };

  const handleRegenerateMessage = async (messageId: string) => {
    if (!actualChatId || !user?.email) return;

    const messageIndex = messages.findIndex(msg => msg.id === messageId);
    if (messageIndex === -1) return;

    const messageToRegenerate = messages[messageIndex];

    // Remover todas as mensagens a partir da mensagem selecionada (inclusive)
    const messagesBeforeRegeneration = messages.slice(0, messageIndex);
    setMessages(messagesBeforeRegeneration);

    // Preparar o conteúdo da mensagem para regenerar
    setMessage(messageToRegenerate.content);
    setIsLoading(true);
    setIsStreaming(true);

    // Preparar ID para a nova mensagem da IA que será criada durante o streaming
    const aiMessageId = aiService.generateMessageId();
    setStreamingMessageId(aiMessageId);

    // Buscar username correto do usuário
    const username = await getUsernameFromFirestore();

    try {
      // Deletar mensagens posteriores do Firebase Storage
      for (let i = messageIndex; i < messages.length; i++) {
        const msgToDelete = messages[i];
        await aiService.deleteMessage(username, actualChatId, msgToDelete.id);
      }

      // Enviar para a IA para regenerar
      await aiService.sendMessageSafe(
        {
          username: username,
          chatId: actualChatId,
          message: messageToRegenerate.content,
          model: selectedModel,
        },
        // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk
        (chunk: string) => {
          setMessages(prev => {
            // Verificar se a mensagem da IA já existe
            const existingMessageIndex = prev.findIndex(msg => msg.id === aiMessageId);

            if (existingMessageIndex !== -1) {
              // Atualizar mensagem existente
              return prev.map(msg =>
                msg.id === aiMessageId
                  ? { ...msg, content: msg.content + chunk }
                  : msg
              );
            } else {
              // Criar nova mensagem da IA na primeira chunk
              // Remover o indicador de loading assim que a primeira chunk chegar
              setIsLoading(false);

              const aiMessage: Message = {
                id: aiMessageId,
                content: chunk,
                sender: 'ai',
                timestamp: new Date().toISOString(),
              };
              return [...prev, aiMessage];
            }
          });
        },
        // onComplete - finalizar streaming
        (fullResponse: string) => {
          setMessages(prev => prev.map(msg =>
            msg.id === aiMessageId
              ? { ...msg, content: fullResponse }
              : msg
          ));
          setIsLoading(false);
          setIsStreaming(false);
          setStreamingMessageId(null);
          setMessage(''); // Limpar o campo de input
        },
        // onError - tratar erros
        (error: string) => {
          console.error('Erro na regeneração:', error);
          setMessages(prev => prev.map(msg =>
            msg.id === aiMessageId
              ? { ...msg, content: `❌ Erro na regeneração: ${error}` }
              : msg
          ));
          setIsLoading(false);
          setIsStreaming(false);
          setStreamingMessageId(null);
          setMessage(''); // Limpar o campo de input
        }
      );
    } catch (error) {
      console.error('Erro ao regenerar mensagem:', error);
      setIsLoading(false);
      setIsStreaming(false);
      setStreamingMessageId(null);
      setMessage(''); // Limpar o campo de input

      // Recarregar mensagens em caso de erro
      loadChatMessages(actualChatId);
    }
  };



  const handleEditMessage = async (messageId: string, newContent: string) => {
    if (!actualChatId || !user?.email) return;

    // Atualizar visualmente primeiro para melhor UX
    setMessages(prev => prev.map(msg =>
      msg.id === messageId ? { ...msg, content: newContent } : msg
    ));

    try {
      const username = await getUsernameFromFirestore();
      const success = await aiService.updateMessage(username, actualChatId, messageId, newContent);

      if (!success) {
        // Se falhou, restaurar o conteúdo original
        loadChatMessages(actualChatId);
        console.error('Falha ao atualizar mensagem no servidor');
      }
    } catch (error) {
      // Se falhou, restaurar o conteúdo original
      loadChatMessages(actualChatId);
      console.error('Erro ao atualizar mensagem:', error);
    }
  };

  const handleCopyMessage = (content: string) => {
    navigator.clipboard.writeText(content).then(() => {
      console.log('Mensagem copiada para a área de transferência');
    });
  };

  // Funções de navegação
  const handleScrollToTop = () => {
    chatInterfaceRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleScrollToBottom = () => {
    chatInterfaceRef.current?.scrollTo({
      top: chatInterfaceRef.current.scrollHeight,
      behavior: 'smooth'
    });
  };

  const handleFullscreenToggle = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Função para converter mensagens para o formato ChatMessage
  const convertToChatMessages = (messages: Message[]): ChatMessage[] => {
    return messages.map(msg => ({
      id: msg.id,
      content: msg.content,
      role: msg.sender === 'user' ? 'user' : 'assistant',
      timestamp: new Date(msg.timestamp).getTime(),
      isFavorite: msg.isFavorite || false,
      attachments: msg.attachments || []
    }));
  };

  // Função para abrir o modal de download
  const handleDownloadModal = () => {
    setIsDownloadModalOpen(true);
  };

  // Sincronizar estado quando currentChat mudar
  useEffect(() => {
    // Limpar mensagens quando mudar para um chat diferente ou para área inicial
    if (currentChat !== actualChatId) {
      setMessages([]);
    }
    setActualChatId(currentChat);
  }, [currentChat]);

  return (
    <div className={`flex-1 flex flex-col h-screen ${isFullscreen ? 'fixed inset-0 z-50 bg-gradient-rafthor' : ''}`}>
      {/* Upperbar */}
      <Upperbar
        currentChat={currentChat}
        chatName={chatName}
        aiModel={selectedModel}
        isFullscreen={isFullscreen}
        onFullscreenToggle={handleFullscreenToggle}
        onDownload={handleDownloadModal}
        isLoading={isLoading}
        attachmentsCount={0}
        aiMetadata={{
          usedCoT: false
        }}
      />

      {/* ChatInterface */}
      <div ref={chatInterfaceRef} className="flex-1 min-h-0 overflow-hidden" style={{ height: 'calc(100vh - 200px)' }}>
        <ChatInterface
          messages={messages}
          isLoading={isLoading}
          isLoadingChat={isLoadingChat}
          onDeleteMessage={handleDeleteMessage}
          onRegenerateMessage={handleRegenerateMessage}
          onEditMessage={handleEditMessage}
          onCopyMessage={handleCopyMessage}
        />
      </div>

      {/* InputBar */}
      <InputBar
        message={message}
        setMessage={setMessage}
        onSendMessage={handleSendMessage}
        isLoading={isLoading}
        selectedModel={selectedModel}
        onModelChange={handleModelChange}
        onScrollToTop={handleScrollToTop}
        onScrollToBottom={handleScrollToBottom}
        isStreaming={isStreaming}
        onCancelStreaming={handleCancelStreaming}
        onOpenModelModal={() => setIsModelModalOpen(true)}
        username={currentUsername}
        chatId={actualChatId || undefined}
      />

      {/* Download Modal */}
      <DownloadModal
        isOpen={isDownloadModalOpen}
        onClose={() => setIsDownloadModalOpen(false)}
        messages={convertToChatMessages(messages)}
        chatName={chatName}
      />

      {/* Model Selection Modal */}
      <ModelSelectionModal
        isOpen={isModelModalOpen}
        onClose={() => setIsModelModalOpen(false)}
        currentModel={selectedModel}
        onModelSelect={handleModelChange}
      />
    </div>
  );
}
