// Interfaces para o sistema de mensagens dos chats

export interface AttachmentMetadata {
  id: string;
  type: 'image' | 'pdf';
  filename: string;
  url: string;
  size: number;
  uploadedAt: number;
  base64Data?: string; // Para PDFs, armazenar o base64 aqui
  chatName?: string; // Nome do chat onde o anexo foi enviado
  storagePath?: string; // Caminho no Firebase Storage
}

export interface TemporaryAttachmentMetadata extends AttachmentMetadata {
  expiresAt: number; // Timestamp de quando o arquivo expira (uploadedAt + 10 horas)
  isTemporary: true; // Flag para identificar anexos temporários
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
  attachments?: AttachmentMetadata[];
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
    cost: number;
  };
  responseTime?: number; // tempo de resposta em ms
  typingSpeed?: number; // velocidade de digitação estimada (caracteres por minuto)
  isFavorite?: boolean;
}

export interface FavoriteMessage {
  id: string; // ID da mensagem favorita
  messageId: string; // ID da mensagem original
  chatId: string; // ID do chat onde a mensagem está
  chatName: string; // Nome do chat para exibição
  role: 'user' | 'assistant';
  content: string;
  timestamp: number; // timestamp da mensagem original
  favoritedAt: number; // timestamp quando foi favoritada
  attachments?: AttachmentMetadata[];
}

// Interfaces para modelos de IA
export interface AIModel {
  id: string;
  name: string;
  description?: string;
  context_length: number;
  pricing: {
    prompt: string;
    completion: string;
    image?: string;
  };
  architecture?: {
    input_modalities: string[];
    output_modalities: string[];
    tokenizer: string;
  };
  created?: number;
  isFavorite?: boolean;
}

export interface ModelProvider {
  id: string;
  name: string;
  endpoint: string;
  apiKey: string;
  models: AIModel[];
}

// Tipos para filtros de modelos
export type ModelCategory = 'paid' | 'free' | 'favorites';
export type ModelSortBy = 'newest' | 'price_low' | 'price_high' | 'context_high';

export interface ModelFilters {
  category: ModelCategory;
  sortBy: ModelSortBy;
  searchTerm: string;
}

export interface Chat {
  id: string;
  name: string;
  messages: ChatMessage[];
  createdAt: string;
  lastUpdated: string;
}

export type DownloadType = 'all' | 'user' | 'ai';

export interface ChatData {
  id: string;
  name: string;
  messages: ChatMessage[];
  context: string;
  system_prompt: string;
  temperature: number;
  maxTokens: number;
  frequency_penalty: number;
  repetition_penalty: number;
  createdAt: number;
  lastUpdatedAt: number;
  chatFolderId?: string;
  lastUsedService?: string;
  lastUsedModel?: string;
  latexInstructions?: boolean;
  password?: string; // senha para proteger o chat
  sessionTime?: {
    totalTime: number; // tempo total acumulado em ms
    lastSessionStart: number; // timestamp do início da sessão atual
    lastUpdated: number; // timestamp da última atualização
  };
}

export interface ChatStorageService {
  createChatFile(userId: string, chatId: string, chatData: Omit<ChatData, 'messages'>): Promise<void>;
  getChatData(userId: string, chatId: string): Promise<ChatData | null>;
  updateChatData(userId: string, chatId: string, chatData: ChatData): Promise<void>;
  addMessage(userId: string, chatId: string, message: MessageInput): Promise<void>;
  deleteChatFile(userId: string, chatId: string): Promise<void>;
}

export interface MessageInput {
  role: 'user' | 'assistant';
  content: string;
  attachments?: AttachmentMetadata[];
}
